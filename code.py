import discord
from discord.ext import commands
import asyncio

# --- CONFIGURATION ---
intents = discord.Intents.default()
intents.message_content = True
intents.guilds = True  # Needed to access guild channels

bot = commands.Bot(command_prefix='!', intents=intents)

# This dictionary defines the roles to be created on the server.
ROLE_STRUCTURE = {
    "Color Roles": [
        {'name': 'Red', 'color': 0xFF0000, 'mentionable': False},
        {'name': 'Blue', 'color': 0x0000FF, 'mentionable': False},
        {'name': 'Green', 'color': 0x00FF00, 'mentionable': False},
        {'name': 'Purple', 'color': 0x800080, 'mentionable': False},
        {'name': 'Orange', 'color': 0xFFA500, 'mentionable': False},
        {'name': 'Pink', 'color': 0xFFC0CB, 'mentionable': False},
        {'name': 'Yellow', 'color': 0xFFFF00, 'mentionable': False},
        {'name': '<PERSON>an', 'color': 0x00FFFF, 'mentionable': False},
        {'name': 'Magenta', 'color': 0xFF00FF, 'mentionable': False},
        {'name': 'Lime', 'color': 0x32CD32, 'mentionable': False}
    ],
    "Hobby & Interest Roles": [
        {'name': 'Artist', 'color': 0x9B59B6, 'mentionable': True},
        {'name': 'Writer', 'color': 0x3498DB, 'mentionable': True},
        {'name': 'Vocalist', 'color': 0xE74C3C, 'mentionable': True},
        {'name': 'Streamer', 'color': 0x9146FF, 'mentionable': True},
        {'name': 'Chef', 'color': 0xF39C12, 'mentionable': True},
        {'name': 'Gamer', 'color': 0x2ECC71, 'mentionable': True},
        {'name': 'Reader', 'color': 0x8B4513, 'mentionable': True},
        {'name': 'Photographer', 'color': 0x34495E, 'mentionable': True},
        {'name': 'Musician', 'color': 0xE67E22, 'mentionable': True},
        {'name': 'Programmer', 'color': 0x1ABC9C, 'mentionable': True},
        {'name': 'Fitness Enthusiast', 'color': 0xE91E63, 'mentionable': True},
        {'name': 'Gardener', 'color': 0x4CAF50, 'mentionable': True},
        {'name': 'Movie Buff', 'color': 0x795548, 'mentionable': True},
        {'name': 'Anime Fan', 'color': 0xFF5722, 'mentionable': True},
        {'name': 'Pet Lover', 'color': 0xFFEB3B, 'mentionable': True}
    ]
}

# --- BOT EVENTS AND COMMANDS ---

@bot.event
async def on_ready():
    """Prints a confirmation message to the console when the bot is online."""
    print(f'Logged in as {bot.user.name}')
    print('Bot is ready to create server roles.')

@bot.command()
@commands.has_permissions(administrator=True)
async def setup_roles(ctx):
    """
    Creates roles for the server based on the predefined role structure.
    This command can only be used by an administrator and requires confirmation.
    """
    guild = ctx.guild

    # --- CONFIRMATION STEP ---
    warning_message = await ctx.send(
        f"**NOTICE:** You are about to create roles in `{guild.name}`.\n"
        "This will add new roles to your server.\n\n"
        "To confirm, please type `CONFIRM` within 30 seconds."
    )

    def check(m):
        # Check if the message is 'CONFIRM', from the same user, and in the same channel.
        return m.content == 'CONFIRM' and m.author == ctx.author and m.channel == ctx.channel

    try:
        confirmation = await bot.wait_for('message', timeout=30.0, check=check)
    except asyncio.TimeoutError:
        await warning_message.edit(content="Setup timed out. Action cancelled.")
        return

    await confirmation.delete() # Delete the user's "CONFIRM" message for cleanliness
    await warning_message.edit(content="✅ **Confirmation received.** Proceeding with role creation...")
    await asyncio.sleep(2)

    # --- ROLE CREATION PHASE ---
    print(f"Starting role creation for server: {guild.name}")
    created_roles = []
    skipped_roles = []

    for category_name, roles in ROLE_STRUCTURE.items():
        print(f"Creating roles for category: {category_name}")
        for role_info in roles:
            role_name = role_info['name']
            role_color = role_info['color']
            role_mentionable = role_info['mentionable']

            # Check if role already exists
            existing_role = discord.utils.get(guild.roles, name=role_name)
            if existing_role:
                print(f"  - Role '{role_name}' already exists, skipping...")
                skipped_roles.append(role_name)
                continue

            try:
                new_role = await guild.create_role(
                    name=role_name,
                    color=discord.Color(role_color),
                    mentionable=role_mentionable,
                    reason="Automated role setup"
                )
                print(f"  - Created role: {role_name}")
                created_roles.append(role_name)
            except Exception as e:
                print(f"  - FAILED to create role {role_name}: {e}")

    # --- FINAL MESSAGE ---
    result_message = "**Role setup complete!**\n\n"
    if created_roles:
        result_message += f"✅ **Created {len(created_roles)} roles:**\n"
        result_message += ", ".join(created_roles) + "\n\n"
    if skipped_roles:
        result_message += f"⚠️ **Skipped {len(skipped_roles)} existing roles:**\n"
        result_message += ", ".join(skipped_roles) + "\n\n"

    result_message += "Users can now be assigned these roles!"

    await ctx.send(result_message)
    print("Role setup complete.")

@setup_roles.error
async def setup_roles_error(ctx, error):
    """Handles errors for the !setup_roles command."""
    if isinstance(error, commands.MissingPermissions):
        await ctx.send("Sorry, you must be an administrator to use the `!setup_roles` command.")
    else:
        await ctx.send("An unexpected error occurred. Please check the console for details.")
        print(f"Error during !setup_roles command: {error}")

# --- RUN THE BOT ---
# Replace 'YOUR_BOT_TOKEN' with your actual bot token from the Discord Developer Portal.
bot.run('MTE4MTMzMzAzMTE0Mzg4Njk0MA.Gbixps.px1XugYuUHzFx8BmGKRuc8YchXbJMgkV3Ts4hY')