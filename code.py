import discord
from discord.ext import commands
import asyncio

# --- CONFIGURATION ---
intents = discord.Intents.default()
intents.message_content = True
intents.guilds = True  # Needed to access guild channels

bot = commands.Bot(command_prefix='!', intents=intents)

# This dictionary defines the entire server structure.
SERVER_STRUCTURE = {
    "WELCOME AND INFO": [
        {'name': 'welcome-and-rules', 'type': 'text', 'topic': 'A warm welcome, server purpose, and rules for our sanctuary.'},
        {'name': 'announcements', 'type': 'text', 'topic': 'Server updates, positive news, and weekly wellness reminders.'},
        {'name': 'role-assignment', 'type': 'text', 'topic': 'React to get roles for your hobbies and interests here.'},
        {'name': 'introductions', 'type': 'text', 'topic': 'Say hello and share a little about what helps you unwind.'}
    ],
    "THE LOUNGE": [
        {'name': 'chat', 'type': 'text', 'topic': 'General chat.'},
        {'name': 'positivity-zone', 'type': 'text', 'topic': 'A dedicated space for sharing good news, compliments, and happy moments.'},
        {'name': 'pets-and-plants', 'type': 'text', 'topic': 'A wholesome channel for sharing pictures of pets, gardens, and houseplants.'},
        {'name': 'question-of-the-day', 'type': 'text', 'topic': 'A bot can post a simple question each day to spark easy conversation.'}
    ],
    "HOBBY AND CREATIVE NOOKS": [
        {'name': 'media', 'type': 'text', 'topic': 'For memes and media should you wish not to clutter the chat channel with links or funny imagery.'},
        {'name': 'creative-corner', 'type': 'text', 'topic': 'Show stuff you\'ve drawn, written, or crafted.'},
        {'name': 'book-club', 'type': 'text', 'topic': 'A quiet corner for talking about books and sharing recommendations.'},
        {'name': 'gaming-guild', 'type': 'text', 'topic': 'For all things video games, from finding people to play with to discussing new releases.'},
        {'name': 'kitchen-creations', 'type': 'text', 'topic': 'A space for sharing recipes, food pictures, and cooking tips.'}
    ],
    "THE GARDEN": [
        {'name': 'mindfulness-resources', 'type': 'text', 'topic': 'For sharing articles, videos, or tips on mental well-being.'},
        {'name': 'lofi-and-chill-music', 'type': 'text', 'topic': 'Share links to relaxing music playlists perfect for studying or chilling out.'},
        {'name': 'the-vent', 'type': 'text', 'topic': 'Vent or rant about your frustrations.'}
    ],
    "VOICE CHATS": [
        {'name': 'General Hangout', 'type': 'voice', 'topic': None},
        {'name': 'Movie Night', 'type': 'voice', 'topic': None},
        {'name': 'Gaming Co-op', 'type': 'voice', 'topic': None},
    ]
}

# --- BOT EVENTS AND COMMANDS ---

@bot.event
async def on_ready():
    """Prints a confirmation message to the console when the bot is online."""
    print(f'Logged in as {bot.user.name}')
    print('Bot is ready to set up a server.')

@bot.command()
@commands.has_permissions(administrator=True)
async def setup(ctx):
    """
    Deletes all channels and categories and then sets up the server from a template.
    This command can only be used by an administrator and requires confirmation.
    """
    guild = ctx.guild

    # --- CONFIRMATION STEP ---
    warning_message = await ctx.send(
        f"**DANGER:** You are about to delete ALL existing channels and categories in `{guild.name}`.\n"
        "This action is **IRREVERSIBLE**.\n\n"
        "To confirm, please type `CONFIRM` within 30 seconds."
    )

    def check(m):
        # Check if the message is 'CONFIRM', from the same user, and in the same channel.
        return m.content == 'CONFIRM' and m.author == ctx.author and m.channel == ctx.channel

    try:
        confirmation = await bot.wait_for('message', timeout=30.0, check=check)
    except asyncio.TimeoutError:
        await warning_message.edit(content="Setup timed out. Action cancelled.")
        return
    
    await confirmation.delete() # Delete the user's "CONFIRM" message for cleanliness
    await warning_message.edit(content="✅ **Confirmation received.** Proceeding with server wipe and setup...")
    await asyncio.sleep(2)

    # --- DELETION PHASE ---
    print(f"Starting channel deletion for server: {guild.name}")
    # We iterate over a copy of the list because we are deleting from the original
    for channel in list(guild.channels):
        try:
            await channel.delete(reason="Automated server setup")
            print(f"  - Deleted channel: {channel.name}")
        except Exception as e:
            print(f"  - FAILED to delete channel {channel.name}: {e}")
    print("Channel deletion phase complete.")

    # --- CREATION PHASE ---
    print(f"Starting channel creation for server: {guild.name}")
    final_message_channel = None

    for category_name, channels in SERVER_STRUCTURE.items():
        new_category = await guild.create_category(name=category_name)
        print(f"Created category: {category_name}")
        for channel_info in channels:
            channel_name = channel_info['name']
            channel_type = channel_info['type']
            channel_topic = channel_info.get('topic')
            
            if channel_type == 'text':
                new_channel = await guild.create_text_channel(
                    name=channel_name, topic=channel_topic, category=new_category
                )
                print(f"  - Created text channel: #{channel_name}")
                if final_message_channel is None: # Save the first text channel created
                    final_message_channel = new_channel
            elif channel_type == 'voice':
                await guild.create_voice_channel(name=channel_name, category=new_category)
                print(f"  - Created voice channel: {channel_name}")

    # --- FINAL MESSAGE ---
    if final_message_channel:
        await final_message_channel.send("**Server setup complete!** Your sanctuary is ready.")
    print("Server setup complete.")

@setup.error
async def setup_error(ctx, error):
    """Handles errors for the !setup command."""
    if isinstance(error, commands.MissingPermissions):
        await ctx.send("Sorry, you must be an administrator to use the `!setup` command.")
    else:
        await ctx.send("An unexpected error occurred. Please check the console for details.")
        print(f"Error during !setup command: {error}")

# --- RUN THE BOT ---
# Replace 'YOUR_BOT_TOKEN' with your actual bot token from the Discord Developer Portal.
bot.run('MTE4MTMzMzAzMTE0Mzg4Njk0MA.Gbixps.px1XugYuUHzFx8BmGKRuc8YchXbJMgkV3Ts4hY')